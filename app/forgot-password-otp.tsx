import { Ionicons } from '@expo/vector-icons';
import { router, useLocalSearchParams } from 'expo-router';
import React, { useRef, useState } from 'react';
import {
  Image,
  SafeAreaView,
  StatusBar,
  Text,
  TextInput,
  TouchableOpacity,
  View,
} from 'react-native';

import { useLanguage } from '@/hooks/useLanguage';

export default function ForgotPasswordOTP() {
  const { t } = useLanguage();
  const { email } = useLocalSearchParams<{ email: string }>();
  const [otp, setOtp] = useState(['', '', '', '']);
  const [activeIndex, setActiveIndex] = useState(0);
  const inputRefs = useRef<(TextInput | null)[]>([]);

  const handleOtpChange = (value: string, index: number) => {
    // Only allow single digit
    if (value.length > 1) return;

    const newOtp = [...otp];
    newOtp[index] = value;
    setOtp(newOtp);

    // Auto-focus next input
    if (value && index < 3) {
      inputRefs.current[index + 1]?.focus();
      setActiveIndex(index + 1);
    } else if (!value && index > 0) {
      inputRefs.current[index - 1]?.focus();
      setActiveIndex(index - 1);
    } else {
      setActiveIndex(index);
    }
  };

  const handleKeyPress = (key: string, index: number) => {
    if (key === 'Backspace' && !otp[index] && index > 0) {
      inputRefs.current[index - 1]?.focus();
      setActiveIndex(index - 1);
    }
  };

  const handleVerify = () => {
    const otpCode = otp.join('');
    if (otpCode.length === 4) {
      // Navigate to reset password screen
      router.push('/forgot-password-reset');
    }
  };

  const handleResendOTP = () => {
    // Reset OTP and show resend logic
    setOtp(['', '', '', '']);
    setActiveIndex(0);
    console.log('Resending OTP to:', email);
  };



  return (
    <SafeAreaView className="flex-1 bg-white">
      <StatusBar barStyle="dark-content" backgroundColor="white" />
      <View className="flex-1 px-6 pt-16">
        {/* Logo */}
        <View className="items-center mb-12">
          <Image
            source={require('../assets/images/logo.png')}
            className="w-40 h-16"
            resizeMode="contain"
          />
        </View>

        {/* Title and Subtitle */}
        <View className="mb-8">
          <Text className="mb-2 text-2xl text-gray-900 font-cairo-bold">
            {t('auth.forgotPassword.otpTitle')}
          </Text>
          <View className="flex-row items-center">
            <Text className="text-base text-gray-500 font-cairo-regular">
              {t('auth.forgotPassword.otpSubtitle')} 
            </Text>
            <Text className="ml-1 text-base text-black font-cairo-medium">
              {email}
            </Text>
            <TouchableOpacity className="ml-2">
              <Ionicons name="pencil" size={16} color="#000" />
            </TouchableOpacity>
          </View>
        </View>

        {/* OTP Input Boxes */}
        <View className="flex-row justify-center gap-3 mb-6">
          {otp.map((digit, index) => (
            <TextInput
              key={index}
              ref={(ref) => {
                inputRefs.current[index] = ref;
              }}
              className={`w-16 h-16 rounded-lg border-2 text-center text-xl font-cairo-bold text-black ${
                index === activeIndex ? 'border-primary bg-primary/10' : 'border-gray-300 bg-gray-50'
              }`}
              value={digit}
              onChangeText={(value) => handleOtpChange(value, index)}
              onKeyPress={({ nativeEvent }) => handleKeyPress(nativeEvent.key, index)}
              onFocus={() => setActiveIndex(index)}
              keyboardType="numeric"
              maxLength={1}
              selectTextOnFocus
            />
          ))}
        </View>

        {/* Resend OTP */}
        <View className="items-center mb-8">
          <Text className="text-gray-500 font-cairo-regular">
            {t('auth.forgotPassword.otpNotReceived')}{' '}
            <TouchableOpacity onPress={handleResendOTP}>
              <Text className="font-cairo-medium text-primary">
                {t('auth.forgotPassword.resendOTP')}
              </Text>
            </TouchableOpacity>
          </Text>
        </View>

        {/* Verify Button */}
        <TouchableOpacity
          onPress={handleVerify}
          className="items-center justify-center w-full mb-8 rounded-lg h-14 bg-primary"
          disabled={otp.join('').length !== 4}
          style={{
            opacity: otp.join('').length === 4 ? 1 : 0.5,
          }}
        >
          <Text className="text-base text-black font-cairo-medium">
            {t('auth.forgotPassword.verify')}
          </Text>
        </TouchableOpacity>

        {/* Spacer */}
        <View className="flex-1" />

      </View>
    </SafeAreaView>
  );
}
