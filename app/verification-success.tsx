import { router } from 'expo-router';
import React, { useEffect } from 'react';
import {
  Image,
  SafeAreaView,
  StatusBar,
  Text,
  View,
} from 'react-native';

import { useLanguage } from '@/hooks/useLanguage';

export default function VerificationSuccess() {
  const { t } = useLanguage();

  useEffect(() => {
    // Auto-redirect to main tabs after 2 seconds
    const timer = setTimeout(() => {
      router.replace('/(tabs)');
    }, 2000);

    return () => clearTimeout(timer);
  }, []);

  return (
    <SafeAreaView className="flex-1 bg-white">
      <StatusBar barStyle="dark-content" backgroundColor="#ffffff" />
      
      {/* Content */}
      <View className="items-center justify-center flex-1 px-6">
        {/* Success Icon */}
        <View className="items-center mb-8">
          <Image
            source={require('@/assets/images/auth/check.png')}
            className="w-32 h-32"
            resizeMode="contain"
          />
        </View>

        {/* Success Title */}
        <Text className="text-2xl text-center text-gray-900 font-cairo-bold">
          {t('auth.verification.success.title')}
        </Text>
      </View>
    </SafeAreaView>
  );
}
