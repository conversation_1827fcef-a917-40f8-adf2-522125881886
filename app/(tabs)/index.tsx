import React from 'react';
import { Text, View } from 'react-native';

export default function HomeScreen() {
  return (
    <View className="flex-1 bg-white justify-center items-center px-6">
      {/* Main Content */}
      <View className="items-center space-y-6">
        {/* Icon/Logo placeholder */}
        <View className="w-24 h-24 bg-primary rounded-full justify-center items-center mb-4">
          <Text className="text-4xl">🚚</Text>
        </View>

        {/* Coming Soon Text */}
        <Text className="text-4xl font-cairo-bold text-gray-800 text-center">
          Coming Soon
        </Text>

        {/* Subtitle */}
        <Text className="text-lg font-cairo-medium text-gray-600 text-center max-w-sm">
          We're working hard to bring you an amazing shipping experience
        </Text>

        {/* Description */}
        <Text className="text-base font-cairo-regular text-gray-500 text-center max-w-xs leading-6">
          Stay tuned for updates and new features
        </Text>

        {/* Animated dots */}
        <View className="flex-row space-x-2 mt-8">
          <View className="w-3 h-3 bg-primary rounded-full animate-pulse"></View>
          <View className="w-3 h-3 bg-primary rounded-full animate-pulse" style={{animationDelay: '0.2s'}}></View>
          <View className="w-3 h-3 bg-primary rounded-full animate-pulse" style={{animationDelay: '0.4s'}}></View>
        </View>
      </View>

      {/* Footer */}
      <View className="absolute bottom-10 items-center">
        <Text className="text-sm font-cairo-medium text-gray-400">
          Safqa Shipping
        </Text>
      </View>
    </View>
  );
}
