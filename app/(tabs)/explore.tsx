import React from 'react';
import { Text, View } from 'react-native';

export default function TabTwoScreen() {
  return (
    <View className="flex-1 bg-white justify-center items-center px-6">
      {/* Main Content */}
      <View className="items-center space-y-6">
        {/* Icon/Logo placeholder */}
        <View className="w-24 h-24 bg-primary rounded-full justify-center items-center mb-4">
          <Text className="text-4xl">🔍</Text>
        </View>

        {/* Coming Soon Text */}
        <Text className="text-4xl font-cairo-bold text-gray-800 text-center">
          Explore
        </Text>

        {/* Subtitle */}
        <Text className="text-lg font-cairo-medium text-gray-600 text-center max-w-sm">
          Discover new features and explore our services
        </Text>

        {/* Description */}
        <Text className="text-base font-cairo-regular text-gray-500 text-center max-w-xs leading-6">
          More content coming soon to help you navigate and explore
        </Text>

        {/* Feature list */}
        <View className="mt-8 space-y-3">
          <View className="flex-row items-center space-x-3">
            <View className="w-2 h-2 bg-primary rounded-full"></View>
            <Text className="text-sm font-cairo-medium text-gray-600">Track shipments</Text>
          </View>
          <View className="flex-row items-center space-x-3">
            <View className="w-2 h-2 bg-primary rounded-full"></View>
            <Text className="text-sm font-cairo-medium text-gray-600">Manage deliveries</Text>
          </View>
          <View className="flex-row items-center space-x-3">
            <View className="w-2 h-2 bg-primary rounded-full"></View>
            <Text className="text-sm font-cairo-medium text-gray-600">View history</Text>
          </View>
        </View>
      </View>

      {/* Footer */}
      <View className="absolute bottom-10 items-center">
        <Text className="text-sm font-cairo-medium text-gray-400">
          More features coming soon
        </Text>
      </View>
    </View>
  );
}
