import { LanguageSelector } from '@/components/LanguageSelector';
import OnboardingSlider from '@/components/OnboardingSlider';
import { useLanguage } from '@/hooks/useLanguage';
import { useRouter } from 'expo-router';
import React from 'react';
import {
  Image,
  SafeAreaView,
  StatusBar,
  Text,
  TouchableOpacity,
  View,
} from 'react-native';

export default function OnboardingScreen() {
  const router = useRouter();
  const { t } = useLanguage();

  const onboardingData = [
    {
      id: '1',
      image: require('@/assets/images/onboarding/1.png'),
      title: t('onboarding.slide1.title'),
      description: t('onboarding.slide1.description'),
    },
    {
      id: '2',
      image: require('@/assets/images/onboarding/2.png'),
      title: t('onboarding.slide2.title'),
      description: t('onboarding.slide2.description'),
    },
    {
      id: '3',
      image: require('@/assets/images/onboarding/3.png'),
      title: t('onboarding.slide3.title'),
      description: t('onboarding.slide3.description'),
    },
  ];

  const handleSignIn = () => {
    // Navigate to sign in screen
    console.log('Navigate to sign in');
    router.push('/signin');
  };

  const handleJoinNow = () => {
    // Navigate to registration screen
    console.log('Navigate to registration');
    router.push('/signup');
  };

  return (
    <SafeAreaView className="flex-1 bg-white">
      <StatusBar barStyle="dark-content" backgroundColor="#ffffff" />

      {/* Header with Logo and Language Selector */}
      <View className="flex-row items-center justify-between px-6 pt-12 pb-8">
        {/* Logo */}
        <View className="flex-1 items-center">
          <Image
            source={require('@/assets/images/logo.png')}
            className="h-20 w-52"
            resizeMode="contain"
          />
        </View>

        {/* Language Selector */}
        <View className="absolute right-6 top-12">
          <LanguageSelector size="small" showLabel={true} />
        </View>
      </View>

      {/* Slider */}
      <View className="flex-1">
        <OnboardingSlider slides={onboardingData} />
      </View>

      {/* Buttons */}
      <View className="px-6 pb-6">
        {/* Sign In Button */}
        <TouchableOpacity
          onPress={handleSignIn}
          className="py-4 mb-6 bg-primary rounded-xl"
          activeOpacity={0.8}
        >
          <Text className="text-lg text-center text-black font-cairo-semibold">
            {t('onboarding.signIn')}
          </Text>
        </TouchableOpacity>

        {/* Join Now Button */}
        <TouchableOpacity
          onPress={handleJoinNow}
          className="p-3 w-full border border-[#E6E6E6] rounded-xl"
          activeOpacity={0.8}
        >
          <Text className="text-base text-center text-black font-cairo-regular">
            {t('onboarding.joinNow')}
          </Text>
        </TouchableOpacity>
      </View>
    </SafeAreaView>
  );
}
