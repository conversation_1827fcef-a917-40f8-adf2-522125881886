import { useLanguage } from '@/hooks/useLanguage';
import { type LanguageType } from '@/utils/languageStorage';
import { useRouter } from 'expo-router';
import React, { useState } from 'react';
import {
  Image,
  SafeAreaView,
  StatusBar,
  Text,
  TouchableOpacity,
  View,
} from 'react-native';

export default function ChooseLanguageScreen() {
  const router = useRouter();
  const { t, changeLanguage, currentLanguageType } = useLanguage();

  const [selectedLanguage, setSelectedLanguage] = useState<LanguageType>(currentLanguageType || 'english');

  const handleLanguageSelect = (language: LanguageType) => {
    setSelectedLanguage(language);
  };

  const handleContinue = async () => {
    try {
      // Save the selected language
      await changeLanguage(selectedLanguage);
      console.log('Selected language saved:', selectedLanguage);

      // Navigate to onboarding screen
      router.push('/onboarding');
    } catch (error) {
      console.error('Error saving language:', error);
      // Still navigate even if saving fails
      router.push('/onboarding');
    }
  };

  const languages = [
    {
      type: 'english' as LanguageType,
      label: t('chooseLanguage.english'),
      flag: '🇺🇸',
    },
    {
      type: 'arabic' as LanguageType,
      label: t('chooseLanguage.arabic'),
      flag: '🇸🇦',
    },
  ];

  return (
    <SafeAreaView className="flex-1 bg-white">
      <StatusBar barStyle="dark-content" backgroundColor="#ffffff" />

      {/* Header with Logo */}
      <View className="px-6 pt-8 pb-4">
        <Image
          source={require('../assets/images/logo.png')}
          className="w-40 h-20"
          resizeMode="contain"
        />
      </View>

      {/* Content */}
      <View className="flex-1 px-6">
        {/* Title and Description */}
        <View className="mb-8">
          <Text className="mb-3 text-2xl text-gray-900 font-cairo-bold">
            {t('chooseLanguage.title')}
          </Text>
          <Text className="text-base leading-6 text-gray-600 font-cairo-regular">
            {t('chooseLanguage.description')}
          </Text>
        </View>

        {/* Language Selection List */}
        <View className="mb-8">
          {languages.map((language) => (
            <TouchableOpacity
              key={language.type}
              className={`flex-row items-center p-4 mb-4 rounded-xl border-2 ${
                selectedLanguage === language.type
                  ? 'border-primary bg-primary/5'
                  : 'border-gray-200 bg-white'
              }`}
              onPress={() => handleLanguageSelect(language.type)}
              activeOpacity={0.8}
            >
              {/* Flag */}
              <Text className="mr-4 text-2xl">{language.flag}</Text>

              {/* Language Label */}
              <Text className="flex-1 text-lg text-gray-900 font-cairo-medium">
                {language.label}
              </Text>

              {/* Radio Button */}
              <View className={`w-6 h-6 rounded-full border-2 ${
                selectedLanguage === language.type
                  ? 'border-primary bg-primary'
                  : 'border-gray-300 bg-white'
              } items-center justify-center`}>
                {selectedLanguage === language.type && (
                  <View className="w-2.5 h-2.5 bg-white rounded-full" />
                )}
              </View>
            </TouchableOpacity>
          ))}
        </View>
      </View>

      {/* Continue Button */}
      <View className="px-6 pb-8">
        <TouchableOpacity
          className="py-4 bg-primary rounded-xl"
          onPress={handleContinue}
          activeOpacity={0.8}
        >
          <Text className="text-lg text-center text-black font-cairo-medium">
            {t('chooseLanguage.continue')}
          </Text>
        </TouchableOpacity>
      </View>
    </SafeAreaView>
  );
}
