import { useLanguage } from '@/hooks/useLanguage';
import { type LanguageType } from '@/utils/languageStorage';
import { useRouter } from 'expo-router';
import React, { useState } from 'react';
import {
  Image,
  ImageBackground,
  SafeAreaView,
  StatusBar,
  Text,
  TouchableOpacity,
  View,
} from 'react-native';

export default function ChooseLanguageScreen() {
  const router = useRouter();
  const { t, changeLanguage } = useLanguage();

  const [selectedLanguage, setSelectedLanguage] = useState<LanguageType>('english');

  const handleLanguageSelect = (language: LanguageType) => {
    setSelectedLanguage(language);
  };
  

  const handleContinue = async () => {
    try {
      // Save the selected language
      await changeLanguage(selectedLanguage);
      console.log('Selected language saved:', selectedLanguage);

      // Navigate to onboarding screen
      router.push('/onboarding');
    } catch (error) {
      console.error('Error saving language:', error);
      // Still navigate even if saving fails
      router.push('/onboarding');
    }
  };

  return (
    <SafeAreaView className="flex-1 bg-gray-50">
      <StatusBar barStyle="dark-content" backgroundColor="#f9fafb" />

      {/* Header with Logo */}
      <View className="px-6 pt-8 pb-4">
        <Image
          source={require('../assets/images/logo.png')}
          className="w-40 h-20"
          resizeMode="contain"
        />
      </View>

      {/* Content */}
      <View className="flex-1 px-6">
        {/* Title and Description */}
        <View className="mb-8">
          <Text className="mb-3 text-2xl text-gray-900 font-cairo-bold">
            {t('chooseLanguage.title')}
          </Text>
          <Text className="text-base leading-6 text-gray-600 font-cairo-regular">
            {t('chooseLanguage.description')}
          </Text>
        </View>

        {/* Language Selection Cards */}
        <View className="flex-row gap-4 mb-8">
          {/* English Card */}
          <TouchableOpacity
            className="flex-1 h-48 overflow-hidden rounded-2xl"
            onPress={() => handleLanguageSelect('english')}
            activeOpacity={0.8}
          >
            <View className="relative flex-1 bg-blue-400">
              {/* Background Image */}
              <ImageBackground
                source={require('../assets/images/onboarding/english.png')}
                className="absolute bottom-0 left-0 right-0 h-20"
                resizeMode="cover"
              />

              {/* Content */}
              <View className="justify-between flex-1 p-4">
                <View className="flex-row justify-between">
                  {/* Language Text */}
                  <Text className="text-lg text-white font-cairo-semibold">
                    {t('chooseLanguage.english')}
                  </Text>

                  {/* Radio Button */}
                  <View className="items-end">
                    <View className={`w-6 h-6 rounded-full border-2 border-white ${selectedLanguage === 'english' ? 'bg-white' : 'bg-transparent'
                      }`}>
                      {selectedLanguage === 'english' && (
                        <View className="w-2.5 h-2.5 bg-blue-400 rounded-full m-auto" />
                      )}
                    </View>
                  </View>
                </View>

                {/* Letter Image */}
                <View className="items-center mb-8">
                  <Image
                    source={require('../assets/images/onboarding/english-letter.png')}
                    className="w-16 h-16"
                    resizeMode="contain"
                  />
                </View>
              </View>
            </View>
          </TouchableOpacity>

          {/* Arabic Card */}
          <TouchableOpacity
            className="flex-1 h-48 overflow-hidden rounded-2xl"
            onPress={() => handleLanguageSelect('arabic')}
            activeOpacity={0.8}
          >
            <View className="relative flex-1 bg-teal-400">
              {/* Background Image */}
              <ImageBackground
                source={require('../assets/images/onboarding/arabic.png')}
                className="absolute bottom-0 left-0 right-0 h-20"
                resizeMode="cover"
              />

              {/* Content */}
              <View className="justify-between flex-1 p-4">
                <View className="flex-row justify-between">
                  {/* Language Text */}
                  <Text className="text-lg text-white font-cairo-semibold">
                    {t('chooseLanguage.arabic')}
                  </Text>

                  {/* Radio Button */}
                  <View className="items-end">
                    <View className={`w-6 h-6 rounded-full border-2 border-white ${selectedLanguage === 'arabic' ? 'bg-white' : 'bg-transparent'
                      }`}>
                      {selectedLanguage === 'arabic' && (
                        <View className="w-2.5 h-2.5 bg-teal-400 rounded-full m-auto" />
                      )}
                    </View>
                  </View>
                </View>
                
                {/* Letter Image */}
                <View className="items-center mb-8">
                  <Image
                    source={require('../assets/images/onboarding/arabic-letter.png')}
                    className="w-16 h-16"
                    resizeMode="contain"
                  />
                </View>
              </View>
            </View>
          </TouchableOpacity>
        </View>
      </View>

      {/* Continue Button */}
      <View className="px-6 pb-8">
        <TouchableOpacity
          className="py-4 bg-primary rounded-xl"
          onPress={handleContinue}
          activeOpacity={0.8}
        >
          <Text className="text-lg text-center text-black font-cairo-medium">
            {t('chooseLanguage.continue')}
          </Text>
        </TouchableOpacity>
      </View>
    </SafeAreaView>
  );
}
