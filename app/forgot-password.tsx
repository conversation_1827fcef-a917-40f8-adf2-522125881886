import { router } from 'expo-router';
import React, { useState } from 'react';
import {
  Image,
  KeyboardAvoidingView,
  Platform,
  SafeAreaView,
  StatusBar,
  Text,
  TextInput,
  TouchableOpacity,
  View,
} from 'react-native';

import { useLanguage } from '@/hooks/useLanguage';

export default function ForgotPassword() {
  const { t } = useLanguage();
  const [email, setEmail] = useState('');

  const handleCancel = () => {
    router.back();
  };

  const handleContinue = () => {
    if (email.trim()) {
      // Navigate to OTP verification screen
      router.push({
        pathname: '/forgot-password-otp',
        params: { email: email.trim() }
      });
    }
  };

  return (
    <SafeAreaView className="flex-1 bg-white">
      <StatusBar barStyle="dark-content" backgroundColor="white" />
      <KeyboardAvoidingView
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
        className="flex-1"
      >
        <View className="flex-1 px-6 pt-16">
          {/* Logo */}
          <View className="items-center mb-12">
            <Image
              source={require('../assets/images/logo.png')}
              className="w-40 h-16"
              resizeMode="contain"
            />
          </View>

          {/* Title and Subtitle */}
          <View className="mb-8">
            <Text className="mb-2 text-2xl text-gray-900 font-cairo-bold">
              {t('auth.forgotPassword.title')}
            </Text>
            <Text className="text-base text-gray-500 font-cairo-regular">
              {t('auth.forgotPassword.subtitle')}
            </Text>
          </View>

          {/* Email Input */}
          <View className="mb-8">
            <TextInput
              className="w-full px-4 text-base text-gray-900 rounded-lg h-14 bg-gray-50 font-cairo-regular"
              placeholder={t('auth.forgotPassword.emailPlaceholder')}
              placeholderTextColor="#9CA3AF"
              value={email}
              onChangeText={setEmail}
              keyboardType="email-address"
              autoCapitalize="none"
              autoCorrect={false}
            />
          </View>

          {/* Spacer to push buttons to bottom */}
          <View className="flex-1" />

          {/* Action Buttons */}
          <View className="flex-row mb-8 gap-x-4">
            {/* Cancel Button */}
            <TouchableOpacity
              onPress={handleCancel}
              className="items-center justify-center flex-1 bg-white border border-gray-300 rounded-lg h-14"
            >
              <Text className="text-base text-gray-700 font-cairo-medium">
                {t('auth.forgotPassword.cancel')}
              </Text>
            </TouchableOpacity>

            {/* Continue Button */}
            <TouchableOpacity
              onPress={handleContinue}
              className="items-center justify-center flex-1 rounded-lg h-14 bg-primary"
              disabled={!email.trim()}
              style={{
                opacity: email.trim() ? 1 : 0.5,
              }}
            >
              <Text className="text-base text-black font-cairo-medium">
                {t('auth.forgotPassword.continue')}
              </Text>
            </TouchableOpacity>
          </View>

         
        </View>
      </KeyboardAvoidingView>
    </SafeAreaView>
  );
}
