import { useLanguage } from '@/hooks/useLanguage';
import { type LanguageType } from '@/utils/languageStorage';
import React from 'react';
import {
  Text,
  TouchableOpacity,
  View
} from 'react-native';

interface LanguageSelectorProps {
  size?: 'small' | 'medium';
}

export const LanguageSelector: React.FC<LanguageSelectorProps> = ({
  size = 'small',
}) => {
  const { currentLanguageType, changeLanguage } = useLanguage();

  const handleToggle = async () => {
    try {
      const newLanguage: LanguageType = currentLanguageType === 'english' ? 'arabic' : 'english';
      await changeLanguage(newLanguage);
    } catch (error) {
      console.error('Error changing language:', error);
    }
  };

  const isEnglish = currentLanguageType === 'english';

  const sizeClasses = {
    small: {
      container: 'h-8 w-20',
      text: 'text-xs',
      flag: 'text-sm',
    },
    medium: {
      container: 'h-10 w-24',
      text: 'text-sm',
      flag: 'text-base',
    },
  };

  const currentSize = sizeClasses[size];

  return (
    <TouchableOpacity
      onPress={handleToggle}
      className={`${currentSize.container} bg-gray-200 rounded-full flex-row items-center relative`}
      activeOpacity={0.8}
    >
      {/* Sliding Background */}
      <View
        className={`absolute top-0 bottom-0 w-1/2 bg-white rounded-full transition-all duration-300 ${
          isEnglish ? 'left-0' : 'right-0'
        }`}
      />

      {/* English Side */}
      <View className="z-10 flex-row items-center justify-center flex-1 py-1">
        {isEnglish && (
          <View className="items-center justify-center w-5 h-5 mr-2 overflow-hidden bg-blue-600 rounded-full">
            <Text className="text-xs text-white">🇬🇧</Text>
          </View>
        )}
        <Text className={`${currentSize.text} font-cairo-bold ${
          isEnglish ? 'text-black' : 'text-gray-400'
        }`}>
          EN
        </Text>
      </View>

      {/* Arabic Side */}
      <View className="z-10 flex-row items-center justify-center flex-1 py-1">
        <Text className={`${currentSize.text} font-cairo-bold ${
          !isEnglish ? 'text-black' : 'text-gray-400'
        }`}>
          AR
        </Text>
        {!isEnglish && (
          <View className="items-center justify-center w-5 h-5 ml-2 overflow-hidden bg-green-600 rounded-full">
            <Text className="text-xs text-white">🇸🇦</Text>
          </View>
        )}
      </View>
    </TouchableOpacity>
  );
};
