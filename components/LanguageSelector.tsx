import { useLanguage } from '@/hooks/useLanguage';
import { type LanguageType } from '@/utils/languageStorage';
import React from 'react';
import {
  Text,
  TouchableOpacity,
  View
} from 'react-native';

interface LanguageSelectorProps {
  size?: 'small' | 'medium';
}

export const LanguageSelector: React.FC<LanguageSelectorProps> = ({
  size = 'small',
}) => {
  const { currentLanguageType, changeLanguage } = useLanguage();

  const handleToggle = async () => {
    try {
      const newLanguage: LanguageType = currentLanguageType === 'english' ? 'arabic' : 'english';
      await changeLanguage(newLanguage);
    } catch (error) {
      console.error('Error changing language:', error);
    }
  };

  const isEnglish = currentLanguageType === 'english';

  const sizeClasses = {
    small: {
      container: 'h-8 w-20',
      text: 'text-xs',
      flag: 'text-sm',
    },
    medium: {
      container: 'h-10 w-24',
      text: 'text-sm',
      flag: 'text-base',
    },
  };

  const currentSize = sizeClasses[size];

  return (
    <TouchableOpacity
      onPress={handleToggle}
      className={`${currentSize.container} bg-gray-100 rounded-full flex-row items-center justify-between px-1 relative`}
      activeOpacity={0.8}
    >
      {/* Toggle Background */}
      <View className="absolute inset-0 bg-gray-100 rounded-full" />

      {/* Active Side Background */}
      <View
        className={`absolute top-0.5 bottom-0.5 w-1/2 bg-white rounded-full transition-all duration-200 ${
          isEnglish ? 'left-0.5' : 'right-0.5'
        }`}
        style={{
          shadowColor: '#000',
          shadowOffset: { width: 0, height: 1 },
          shadowOpacity: 0.1,
          shadowRadius: 2,
          elevation: 2,
        }}
      />

      {/* English Side */}
      <View className={`flex-1 flex-row items-center justify-center z-10 ${
        isEnglish ? 'opacity-100' : 'opacity-60'
      }`}>
        <Text className={`${currentSize.flag} mr-1`}>🇬🇧</Text>
        <Text className={`${currentSize.text} font-cairo-semibold ${
          isEnglish ? 'text-gray-900' : 'text-gray-500'
        }`}>
          EN
        </Text>
      </View>

      {/* Arabic Side */}
      <View className={`flex-1 flex-row items-center justify-center z-10 ${
        !isEnglish ? 'opacity-100' : 'opacity-60'
      }`}>
        <Text className={`${currentSize.flag} mr-1`}>🇸🇦</Text>
        <Text className={`${currentSize.text} font-cairo-semibold ${
          !isEnglish ? 'text-gray-900' : 'text-gray-500'
        }`}>
          AR
        </Text>
      </View>
    </TouchableOpacity>
  );
};
