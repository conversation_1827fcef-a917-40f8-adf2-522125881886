import React, { useState } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  Modal,
  Pressable,
} from 'react-native';
import { useLanguage } from '@/hooks/useLanguage';
import { type LanguageType } from '@/utils/languageStorage';

interface LanguageSelectorProps {
  size?: 'small' | 'medium';
  showLabel?: boolean;
}

export const LanguageSelector: React.FC<LanguageSelectorProps> = ({
  size = 'small',
  showLabel = true,
}) => {
  const { currentLanguageType, changeLanguage, t } = useLanguage();
  const [isModalVisible, setIsModalVisible] = useState(false);

  const languages = [
    {
      type: 'english' as LanguageType,
      code: 'EN',
      flag: '🇺🇸',
      label: 'English',
    },
    {
      type: 'arabic' as LanguageType,
      code: 'AR',
      flag: '🇸🇦',
      label: 'العربية',
    },
  ];

  const currentLanguage = languages.find(lang => lang.type === currentLanguageType);

  const handleLanguageChange = async (languageType: LanguageType) => {
    try {
      await changeLanguage(languageType);
      setIsModalVisible(false);
    } catch (error) {
      console.error('Error changing language:', error);
      setIsModalVisible(false);
    }
  };

  const sizeClasses = {
    small: {
      container: 'px-2 py-1',
      text: 'text-sm',
      flag: 'text-base',
    },
    medium: {
      container: 'px-3 py-2',
      text: 'text-base',
      flag: 'text-lg',
    },
  };

  const currentSize = sizeClasses[size];

  return (
    <>
      {/* Language Selector Button */}
      <TouchableOpacity
        onPress={() => setIsModalVisible(true)}
        className={`flex-row items-center bg-white border border-gray-200 rounded-lg ${currentSize.container}`}
        activeOpacity={0.7}
      >
        <Text className={`${currentSize.flag} mr-1`}>
          {currentLanguage?.flag}
        </Text>
        {showLabel && (
          <Text className={`${currentSize.text} font-cairo-medium text-gray-800`}>
            {currentLanguage?.code}
          </Text>
        )}
        <Text className={`${currentSize.text} text-gray-500 ml-1`}>
          ▼
        </Text>
      </TouchableOpacity>

      {/* Language Selection Modal */}
      <Modal
        visible={isModalVisible}
        transparent={true}
        animationType="fade"
        onRequestClose={() => setIsModalVisible(false)}
      >
        <Pressable
          className="flex-1 bg-black/50 justify-center items-center"
          onPress={() => setIsModalVisible(false)}
        >
          <View className="bg-white rounded-xl mx-6 p-4 min-w-[200px]">
            <Text className="text-lg font-cairo-semibold text-center mb-4 text-gray-800">
              {t('chooseLanguage.title')}
            </Text>
            
            {languages.map((language) => (
              <TouchableOpacity
                key={language.type}
                onPress={() => handleLanguageChange(language.type)}
                className={`flex-row items-center p-3 rounded-lg mb-2 ${
                  currentLanguageType === language.type
                    ? 'bg-primary/10 border border-primary'
                    : 'bg-gray-50'
                }`}
                activeOpacity={0.7}
              >
                <Text className="text-xl mr-3">{language.flag}</Text>
                <Text className="flex-1 text-base font-cairo-medium text-gray-800">
                  {language.label}
                </Text>
                {currentLanguageType === language.type && (
                  <View className="w-5 h-5 bg-primary rounded-full items-center justify-center">
                    <Text className="text-white text-xs">✓</Text>
                  </View>
                )}
              </TouchableOpacity>
            ))}
          </View>
        </Pressable>
      </Modal>
    </>
  );
};
